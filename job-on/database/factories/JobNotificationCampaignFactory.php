<?php

namespace Database\Factories;

use App\Models\JobNotificationCampaign;
use App\Enums\JobNotificationStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class JobNotificationCampaignFactory extends Factory
{
    protected $model = JobNotificationCampaign::class;

    public function definition()
    {
        return [
            'job_id' => 'job-' . $this->faker->uuid,
            'job_title' => $this->faker->jobTitle . ' Service',
            'job_description' => $this->faker->paragraph,
            'job_budget' => $this->faker->randomFloat(2, 50, 500),
            'job_zip_code' => $this->faker->postcode,
            'job_address' => $this->faker->address,
            'job_latitude' => $this->faker->latitude,
            'job_longitude' => $this->faker->longitude,
            'job_category' => $this->faker->randomElement(['cleaning', 'plumbing', 'electrical', 'landscaping', 'general']),
            'customer_name' => $this->faker->name,
            'customer_email' => $this->faker->email,
            'customer_phone' => $this->faker->phoneNumber,
            'search_radius' => $this->faker->randomFloat(1, 10, 50),
            'business_count' => $this->faker->numberBetween(0, 20),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected', 'sent', 'pending_scraping']),
            'admin_token' => $this->faker->uuid,
            'token_expires_at' => $this->faker->dateTimeBetween('now', '+7 days'),
            'event_id' => 'event-' . $this->faker->uuid,
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => JobNotificationStatusEnum::PENDING,
                'business_count' => $this->faker->numberBetween(1, 10),
            ];
        });
    }

    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => JobNotificationStatusEnum::APPROVED,
                'approved_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
                'business_count' => $this->faker->numberBetween(1, 10),
            ];
        });
    }

    public function rejected()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => JobNotificationStatusEnum::REJECTED,
                'rejected_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
                'rejection_reason' => $this->faker->sentence,
                'business_count' => 0,
            ];
        });
    }

    public function sent()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => JobNotificationStatusEnum::SENT,
                'sent_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
                'business_count' => $this->faker->numberBetween(1, 10),
            ];
        });
    }

    public function pendingScraping()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
                'business_count' => 0,
            ];
        });
    }
}
