<?php

namespace Database\Factories;

use App\Models\ScrapingJob;
use App\Models\JobNotificationCampaign;
use App\Enums\ScrapingJobStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class ScrapingJobFactory extends Factory
{
    protected $model = ScrapingJob::class;

    public function definition()
    {
        return [
            'job_notification_campaign_id' => JobNotificationCampaign::factory(),
            'location_query' => $this->faker->city . ', ' . $this->faker->stateAbbr . ' ' . $this->faker->postcode,
            'category_query' => $this->faker->randomElement(['cleaning', 'plumbing', 'electrical', 'landscaping', 'general']),
            'status' => $this->faker->randomElement(ScrapingJobStatusEnum::getValues()),
            'progress' => null,
            'discovered_businesses_count' => $this->faker->numberBetween(0, 10),
            'message' => $this->faker->optional()->sentence,
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => ScrapingJobStatusEnum::PENDING,
                'discovered_businesses_count' => 0,
                'message' => null,
            ];
        });
    }

    public function inProgress()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => ScrapingJobStatusEnum::IN_PROGRESS,
                'discovered_businesses_count' => 0,
                'message' => 'Scraping in progress...',
            ];
        });
    }

    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => ScrapingJobStatusEnum::COMPLETED,
                'discovered_businesses_count' => $this->faker->numberBetween(1, 10),
                'message' => 'Scraping completed successfully.',
            ];
        });
    }

    public function failed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => ScrapingJobStatusEnum::FAILED,
                'discovered_businesses_count' => 0,
                'message' => 'Scraping failed: ' . $this->faker->sentence,
            ];
        });
    }
}
