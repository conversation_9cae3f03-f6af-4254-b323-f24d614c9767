<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration creates all the tables needed for the missing APIs:
     * - job_reports: For reporting issues with jobs
     * - provider_availabilities: For provider weekly availability
     * - provider_portfolios: For provider portfolio items
     * - provider_services: For provider custom services
     * - provider_verifications: For provider verification documents
     */
    public function up(): void
    {
        // 1. Create job_reports table
        Schema::create('job_reports', function (Blueprint $table) {
            $table->id();
            $table->string('job_uuid');
            $table->unsignedBigInteger('job_id'); // Changed from job_booking_id to job_id
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('reason');
            $table->text('description');
            $table->enum('status', ['pending', 'reviewed', 'resolved', 'dismissed'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->timestamps();

            // Check if business_jobs table exists before adding foreign key
            if (Schema::hasTable('business_jobs')) {
                $table->foreign('job_id')->references('id')->on('business_jobs')->onDelete('cascade');
            }

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');

            $table->index(['job_uuid']);
            $table->index(['job_id']);
            $table->index(['status']);
        });

        // 2. Create provider_availabilities table
        Schema::create('provider_availabilities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('provider_id');
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->time('start_time');
            $table->time('end_time');
            $table->boolean('is_available')->default(true);
            $table->timestamps();

            $table->foreign('provider_id')->references('id')->on('users')->onDelete('cascade');

            $table->index(['provider_id', 'day_of_week']);
        });

        // 3. Create provider_portfolios table
        Schema::create('provider_portfolios', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->unsignedBigInteger('provider_id');
            $table->string('title');
            $table->text('description');
            $table->string('image_uuid')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('provider_id')->references('id')->on('users')->onDelete('cascade');

            // Check if assets table exists before adding foreign key
            if (Schema::hasTable('assets')) {
                $table->foreign('image_uuid')->references('uuid')->on('assets')->onDelete('set null');
            }

            $table->index(['provider_id']);
            $table->index(['is_active']);
        });

        // 4. Create provider_services table
        Schema::create('provider_services', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->unsignedBigInteger('provider_id');
            $table->string('name');
            $table->decimal('price', 10, 2);
            $table->text('description');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('provider_id')->references('id')->on('users')->onDelete('cascade');

            $table->index(['provider_id']);
            $table->index(['is_active']);
        });

        // 5. Create provider_verifications table
        Schema::create('provider_verifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('provider_id');
            $table->string('document_uuid');
            $table->string('document_type');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamps();

            $table->foreign('provider_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');

            // Check if assets table exists before adding foreign key
            if (Schema::hasTable('assets')) {
                $table->foreign('document_uuid')->references('uuid')->on('assets')->onDelete('cascade');
            }

            $table->index(['provider_id']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('provider_verifications');
        Schema::dropIfExists('provider_services');
        Schema::dropIfExists('provider_portfolios');
        Schema::dropIfExists('provider_availabilities');
        Schema::dropIfExists('job_reports');
    }
};