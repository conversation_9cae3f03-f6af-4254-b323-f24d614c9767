<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Job Notification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the simplified job notification 
    | system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Search Radius
    |--------------------------------------------------------------------------
    |
    | The default radius (in miles) to use when searching for businesses
    | around a job's location.
    |
    */
    'default_radius' => (int) env('JOB_NOTIFICATION_DEFAULT_RADIUS', 30),

    /*
    |--------------------------------------------------------------------------
    | Maximum Search Radius
    |--------------------------------------------------------------------------
    |
    | The maximum radius (in miles) that can be used when searching for 
    | businesses.
    |
    */
    'max_radius' => (int) env('JOB_NOTIFICATION_MAX_RADIUS', 75),

    /*
    |--------------------------------------------------------------------------
    | Admin Email
    |--------------------------------------------------------------------------
    |
    | The email address of the admin who will receive job notification
    | campaign approval requests.
    |
    */
    'admin_email' => env('JOB_NOTIFICATION_ADMIN_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------
    | Token Expiry
    |--------------------------------------------------------------------------
    |
    | The number of hours that a notification approval token remains valid.
    |
    */
    'token_expiry_hours' => (int) env('JOB_NOTIFICATION_TOKEN_EXPIRY_HOURS', 24),
]; 