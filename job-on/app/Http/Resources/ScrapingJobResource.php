<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ScrapingJobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_notification_campaign_id' => $this->job_notification_campaign_id,
            'location_query' => $this->location_query,
            'category_query' => $this->category_query,
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'discovered_businesses_count' => $this->discovered_businesses_count,
            'message' => $this->message,
            'progress' => $this->progress,
            'can_cancel' => $this->canCancel(),
            'can_retry' => $this->canRetry(),
            'duration' => $this->getDuration(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Include campaign information when loaded
            'campaign' => $this->when($this->relationLoaded('jobNotificationCampaign'), function () {
                return [
                    'id' => $this->jobNotificationCampaign->id,
                    'job_id' => $this->jobNotificationCampaign->job_id,
                    'job_title' => $this->jobNotificationCampaign->job_title,
                    'job_zip_code' => $this->jobNotificationCampaign->job_zip_code,
                    'job_category' => $this->jobNotificationCampaign->job_category,
                    'status' => $this->jobNotificationCampaign->status,
                    'business_count' => $this->jobNotificationCampaign->business_count,
                    'created_at' => $this->jobNotificationCampaign->created_at,
                ];
            }),
        ];
    }

    /**
     * Get human-readable status label
     */
    protected function getStatusLabel()
    {
        $labels = [
            'pending' => 'Pending',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'failed' => 'Failed',
        ];

        return $labels[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Check if the scraping job can be cancelled
     */
    protected function canCancel()
    {
        return in_array($this->status, ['pending', 'in_progress']);
    }

    /**
     * Check if the scraping job can be retried
     */
    protected function canRetry()
    {
        return $this->status === 'failed';
    }

    /**
     * Get the duration of the scraping job
     */
    protected function getDuration()
    {
        if ($this->status === 'pending') {
            return null;
        }

        $start = $this->created_at;
        $end = in_array($this->status, ['completed', 'failed']) ? $this->updated_at : now();

        return $start->diffInSeconds($end);
    }
}
