<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Jobs\EnhanceBusinessDataJob;
use App\Services\BusinessDataCompletenessService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BusinessEnhancementController extends Controller
{
    protected $completenessService;

    public function __construct(BusinessDataCompletenessService $completenessService)
    {
        $this->completenessService = $completenessService;
    }


    public function status(): JsonResponse
    {
        try {
            $totalBusinesses = Business::count();
            $completeBusinesses = Business::withCompleteData()->count();
            $incompleteBusinesses = Business::withIncompleteData()->count();
            
            $completenessPercentage = $totalBusinesses > 0 
                ? round(($completeBusinesses / $totalBusinesses) * 100, 2) 
                : 0;

            // Get field analysis
            $fieldAnalysis = $this->getFieldAnalysis();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_businesses' => $totalBusinesses,
                    'complete_businesses' => $completeBusinesses,
                    'incomplete_businesses' => $incompleteBusinesses,
                    'completeness_percentage' => $completenessPercentage,
                    'field_analysis' => $fieldAnalysis,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Business enhancement status error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get enhancement status',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function enhance(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'batch_size' => 'integer|min:1|max:50',
                'priority' => 'string|in:email,coordinates,address,contact,category,business_info,all',
                'business_ids' => 'array',
                'business_ids.*' => 'integer|exists:businesses,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $batchSize = $request->input('batch_size', 10);
            $priority = $request->input('priority', 'email');
            $specificBusinessIds = $request->input('business_ids', []);

            // Get businesses to enhance
            if (!empty($specificBusinessIds)) {
                $businesses = Business::whereIn('id', $specificBusinessIds)->get();
            } else {
                $businesses = $this->getBusinessesToEnhance($priority, $batchSize);
            }

            if ($businesses->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No businesses found that need enhancement',
                    'data' => [
                        'businesses_queued' => 0,
                        'priority' => $priority,
                    ]
                ]);
            }

            $queuedCount = 0;
            $enhancementJobs = [];

            foreach ($businesses as $business) {
                $missingFields = $this->getMissingFieldsByPriority($business, $priority);
                
                if (!empty($missingFields)) {
                    // Dispatch enhancement job with small delay to prevent queue flooding
                    $delay = rand(5, 30); // 5-30 seconds delay
                    EnhanceBusinessDataJob::dispatch($business, $missingFields)
                        ->delay(now()->addSeconds($delay));
                    
                    $queuedCount++;
                    $enhancementJobs[] = [
                        'business_id' => $business->id,
                        'business_name' => $business->name,
                        'missing_fields' => $missingFields,
                        'delay_seconds' => $delay,
                    ];
                }
            }

            Log::info('Business enhancement triggered via API', [
                'businesses_queued' => $queuedCount,
                'priority' => $priority,
                'batch_size' => $batchSize,
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully queued {$queuedCount} businesses for enhancement",
                'data' => [
                    'businesses_queued' => $queuedCount,
                    'priority' => $priority,
                    'enhancement_jobs' => $enhancementJobs,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Business enhancement trigger error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to trigger business enhancement',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    private function getBusinessesToEnhance(string $priority, int $limit): \Illuminate\Database\Eloquent\Collection
    {
        $query = Business::query();

        switch ($priority) {
            case 'email':
                $query->where(function ($q) {
                    $q->whereNull('email')->orWhere('email', '');
                });
                break;
            case 'coordinates':
                $query->where(function ($q) {
                    $q->whereNull('lat')->orWhereNull('lng')
                      ->orWhere('lat', '')->orWhere('lng', '');
                });
                break;
            case 'address':
                $query->where(function ($q) {
                    $q->whereNull('address')->orWhere('address', '')
                      ->orWhereRaw('address NOT REGEXP "[0-9]{5}"');
                });
                break;
            case 'contact':
                $query->where(function ($q) {
                    $q->whereNull('phone')->orWhere('phone', '')
                      ->orWhereNull('website')->orWhere('website', '');
                });
                break;
            case 'category':
                $query->where(function ($q) {
                    $q->whereNull('category')->orWhere('category', '');
                });
                break;
            case 'business_info':
                $query->where(function ($q) {
                    $q->whereNull('website')->orWhere('website', '')
                      ->orWhereNull('description')->orWhere('description', '');
                });
                break;
            default: // 'all'
                $query->withIncompleteData();
        }

        return $query->limit($limit)->get();
    }


    private function getMissingFieldsByPriority(Business $business, string $priority): array
    {
        $allMissingFields = $this->completenessService->getMissingCriticalFields($business);
        
        $priorityMap = [
            'email' => ['email'],
            'coordinates' => ['lat', 'lng'],
            'address' => ['address'],
            'contact' => ['phone', 'website'],
            'category' => ['category'],
            'business_info' => ['website', 'description'],
            'all' => $allMissingFields,
        ];

        if (isset($priorityMap[$priority]) && $priority !== 'all') {
            return array_intersect($allMissingFields, $priorityMap[$priority]);
        }

        return $allMissingFields;
    }


    private function getFieldAnalysis(): array
    {
        $fields = ['email', 'lat', 'lng', 'address', 'phone', 'website', 'category', 'description'];
        $analysis = [];
        
        foreach ($fields as $field) {
            $missing = Business::where(function ($query) use ($field) {
                $query->whereNull($field)->orWhere($field, '');
            })->count();
            
            $analysis[$field] = [
                'missing_count' => $missing,
                'field_name' => ucfirst($field),
            ];
        }
        
        return $analysis;
    }
}