<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\CreateJobBookingRequest;
use App\Http\Requests\API\UpdateJobBookingRequest;
use App\Http\Requests\API\AcceptBidRequest;
use App\Http\Resources\JobBookingResource;
use App\Http\Resources\ExternalJobResource;
use App\Repositories\API\JobBookingRepository;
use App\Services\ExternalJobService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\JobBooking;
use App\Models\Business;
use App\Models\Bid;
use App\Models\Job;
use App\Models\JobReport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class JobBookingController extends Controller
{
    protected $repository;

    public function __construct(JobBookingRepository $repository)
    {
        $this->repository = $repository;

        // Apply authorization middleware, except for index method (public access)
        $this->authorizeResource(JobBooking::class, 'jobBooking', [
            'except' => ['index','store']
        ]);
    }

    /**
     * Display a listing of job bookings.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = $request->only(['status', 'job_type', 'service_category']);
            if ($request->filled('user_id')) {
                $filters['user_id'] = $request->input('user_id');
            }
            if ($request->filled('category')) {
                $filters['service_category'] = $request->input('category');
            }
            $perPage = $request->input('per_page', 15);
            
            $jobs = $this->repository->getAll($filters, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => JobBookingResource::collection($jobs),
                'pagination' => [
                    'current_page' => $jobs->currentPage(),
                    'per_page' => $jobs->perPage(),
                    'total' => $jobs->total(),
                    'last_page' => $jobs->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to list jobs: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to list jobs',
                    'details' => $e->getMessage()
                ]
                ], 500);
            }
    }

    /**
     * Store a newly created job booking in storage.
     *
     * @param  \App\Http\Requests\CreateJobBookingRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateJobBookingRequest $request)
    {
        try {
            $jobData = $request->input('jobData');
            
            // Pass null for userId since we're not requiring authentication for testing
            $job = $this->repository->createJob($jobData, null);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'jobId' => $job->job_uuid,
                    'createdAt' => $job->created_at,
                    'status' => $job->status,
                    'message' => 'Job created successfully. You will be notified when providers respond.'
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create job: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to create job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Display the specified job booking.
     *
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(JobBooking $jobBooking)
    {
        try {
            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($jobBooking)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to fetch job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Update the specified job booking.
     *
     * @param  \App\Http\Requests\UpdateJobBookingRequest  $request
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateJobBookingRequest $request, JobBooking $jobBooking)
    {
        try {
            $jobData = $request->input('jobData');
            $updatedJob = $this->repository->update($jobBooking, $jobData);

            return response()->json([
                'success' => true,
                'data' => new JobBookingResource($updatedJob),
                'message' => 'Job updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified job booking.
     *
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(JobBooking $jobBooking)
    {
        try {
            $this->repository->delete($jobBooking);

            return response()->json([
                'success' => true,
                'message' => 'Job deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete job: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to delete job',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get matching providers for a job
     *
     * @param string $jobUuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProviders($jobUuid)
    {
        try {
            $job = $this->repository->getByUuid($jobUuid);
            
            if (!$job) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_NOT_FOUND',
                        'message' => 'Job not found',
                    ]
                ], 404);
            }
            
            // Build base query for matching providers
            $query = Business::query();
            
            // Match by service category if available
            if ($job->service_category) {
                $query->where('category', $job->service_category);
            }
            
            // Match by location using proper address parsing
            if ($job->state) {
                $query->where(function($q) use ($job) {
                    $q->where('location', 'LIKE', '%' . $job->state . '%')
                      ->orWhere('address', 'LIKE', '%' . $job->state . '%');
                });
            }

            // Get matching businesses
            $businesses = $query->get();
            
            return response()->json([
                'success' => true,
                'data' => $businesses
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get providers: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get providers',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Send job to selected providers
     *
     * @param Request $request
     * @param string $jobUuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendToProviders(Request $request, $jobUuid)
    {
        try {
            $request->validate([
                'provider_uuids' => 'required|array|min:1',
                'provider_uuids.*' => 'required|exists:businesses,business_uuid'
            ]);

            $job = JobBooking::where('job_uuid', $jobUuid)->firstOrFail();
            $businesses = Business::whereIn('business_uuid', $request->provider_uuids)->get();

            foreach ($businesses as $business) {
                if (empty($business->email)) {
                    continue;
                }
                \Mail::to($business->email)
                    ->send(new \App\Mail\NewJobRequest($job, $business));
            }

            return response()->json([
                'success' => true,
                'message' => 'Project has been sent to ' . $businesses->count() . ' providers.'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'JOB_NOT_FOUND',
                    'message' => 'Job not found'
                ]
            ], 404);
        } catch (\Exception $e) {
            Log::error('Failed to send to providers: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to send project to providers',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    public function getMyJobBookings(Request $request)
    {
        $user = auth('api')->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UNAUTHORIZED',
                    'message' => 'Unauthorized',
                ]
            ], 401);
        }
        $perPage = $request->input('per_page', 15);
        $jobs = $this->repository->getAll(['user_id' => $user->id], $perPage);
        return response()->json([
            'success' => true,
            'data' => JobBookingResource::collection($jobs),
            'pagination' => [
                'current_page' => $jobs->currentPage(),
                'per_page' => $jobs->perPage(),
                'total' => $jobs->total(),
                'last_page' => $jobs->lastPage()
            ]
        ]);
    }

    /**
     * Report an issue with a job.
     *
     * @param string $jobUuid
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportIssue(string $jobUuid, Request $request)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'reason' => 'required|string|max:255',
                'description' => 'required|string|max:1000',
            ]);

            // Get the authenticated user
            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHORIZED',
                        'message' => 'Unauthorized',
                    ]
                ], 401);
            }

            // Find the job (from business_jobs table)
            $job = Job::where('job_uuid', $jobUuid)->first();
            if (!$job) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_NOT_FOUND',
                        'message' => 'Job not found',
                    ]
                ], 404);
            }

            // Verify that the user has permission to report this job
            // Either the customer who created the job booking or the provider assigned to the job
            if ($job->customer_id !== $user->id && $job->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only report jobs you are involved in',
                    ]
                ], 403);
            }

            // Create the job report
            $jobReport = JobReport::create([
                'job_uuid' => $jobUuid,
                'job_id' => $job->id,
                'user_id' => $user->id,
                'reason' => $validated['reason'],
                'description' => $validated['description'],
                'status' => 'pending',
            ]);

            return response()->json([
                'message' => 'Issue reported successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to report job issue: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to report job issue',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }



    /**
     * Accept a bid for the specified job booking.
     *
     * @param  \App\Http\Requests\API\AcceptBidRequest  $request
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function acceptBid(AcceptBidRequest $request, JobBooking $jobBooking)
    {
        $this->authorize('acceptBid', $jobBooking);

        $bidId = $request->input('bid_id');
        $notes = $request->input('notes');

        try {
            // Find the bid associated with this job booking
            $bid = $jobBooking->bids()->findOrFail($bidId);

            if (!$bid->canAccept()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'BID_CANNOT_BE_ACCEPTED',
                        'message' => 'This bid cannot be accepted at this time. It might have been already processed or the job booking is not open for bidding.',
                    ]
                ], 422); // Unprocessable Entity
            }

            // Use the existing acceptBid method on the JobBooking model
            $accepted = $jobBooking->acceptBid($bidId, $notes);

            if ($accepted) {
                // Reload the jobBooking with relevant relationships
                // Remove 'acceptedBid' and 'assignedJob' from here
                $jobBooking->load(['bids']); 

                // You can access the accepted bid and assigned job via their methods if needed for the response
                // For example: 
                // $acceptedBidData = $jobBooking->acceptedBid()->toArray();
                // $assignedJobData = $jobBooking->assignedJob() ? $jobBooking->assignedJob()->toArray() : null;

                return response()->json([
                    'success' => true,
                    'message' => 'Bid accepted successfully.',
                    'data' => [
                        'job_booking' => $jobBooking,
                        // Optionally include acceptedBid and assignedJob data explicitly
                        // 'accepted_bid' => $jobBooking->acceptedBid(), 
                        // 'assigned_job' => $jobBooking->assignedJob(), 
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCEPT_BID_FAILED',
                        'message' => 'Failed to accept bid due to an unexpected issue.',
                    ]
                ], 500);
            }
        } catch (ModelNotFoundException $e) {
            Log::error('Failed to accept bid: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to accept bid',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Reject a bid for the specified job booking.
     *
     * @param  \App\Http\Requests\API\RejectBidRequest  $request // We might need a new Request class or reuse/modify an existing one
     * @param  \App\Models\JobBooking  $jobBooking
     * @return \Illuminate\Http\JsonResponse
     */
    public function rejectBid(Request $request, JobBooking $jobBooking) // Consider creating a RejectBidRequest
    {
        try {
            $this->authorize('rejectBid', $jobBooking); // Add authorization policy if needed

            $bidId = $request->input('bid_id');
            // You might want to include 'notes' or 'reason' for rejection as well
            // $notes = $request->input('notes'); 
    
            // Verify the bid belongs to this job booking
            $bid = $jobBooking->bids()->find($bidId);
    
            if (!$bid) {
                return response()->json([
                    'success' => false,
                    'code' => 'BID_NOT_FOUND',
                    'message' => 'Bid not found for this job booking',
                ], 404);
            }
    
            // Check if bid can be rejected (e.g., it's pending)
            // You might need a new method like $bid->canReject() in your Bid model
            if ($bid->status !== 'requested') { // Example check, adjust as per your Bid model logic
                 return response()->json([
                    'success' => false,
                    'code' => 'CANNOT_REJECT_BID',
                    'message' => 'This bid cannot be rejected (it may already be accepted, rejected, or expired).',
                ], 400);
            }
    
            // Reject the bid
            // This might involve updating the bid's status to 'rejected'
            // And potentially calling a method on the JobBooking model if there's related logic
            DB::beginTransaction();
            $bid->status = 'rejected';
            // if ($notes) {
            //    $bid->rejection_notes = $notes; // If you store rejection notes
            // }
            $bid->save();
            
            // Potentially, you might want to notify the provider whose bid was rejected.
            // event(new BidRejected($bid));
            
            DB::commit();
            
            $jobBooking->load(['bids', 'acceptedBid', 'assignedJob']); // Reload relevant data
    
            return response()->json([
                'success' => true,
                'data' => new \App\Http\Resources\JobBookingResource($jobBooking), // Or a specific BidResource
                'message' => 'Bid rejected successfully',
            ]);
    
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return response()->json([
                'success' => false,
                'code' => 'FORBIDDEN',
                'message' => 'You are not authorized to reject this bid.',
            ], 403);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to reject bid: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'code' => 'REJECT_BID_FAILED',
                'message' => 'Failed to reject bid. Please try again.',
                // 'error_details' => $e->getMessage() // For debugging, remove in production
            ], 500);
        }
    }

    /**
     * Get external jobs from external API
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExternalJobs(Request $request)
    {
        try {
            $externalJobService = new ExternalJobService();

            // Get query parameters
            $workspace = $request->input('workspace');
            $limit = $request->input('limit', 15);
            $page = $request->input('page', 1);

            // Validate limit and page
            $limit = max(1, min(100, (int) $limit)); // Between 1 and 100
            $page = max(1, (int) $page);

            // Get external jobs
            $externalData = $externalJobService->getExternalJobsWithDefaults($workspace, $limit, $page);

            // Transform the data using the resource
            $jobs = collect($externalData['data'] ?? [])->map(function ($job) {
                return new ExternalJobResource($job);
            });

            // Extract pagination data
            $pagination = $externalData['pagination'] ?? [];

            return response()->json([
                'success' => true,
                'data' => $jobs,
                'pagination' => [
                    'current_page' => $pagination['page'] ?? $page,
                    'per_page' => $pagination['limit'] ?? $limit,
                    'total' => $pagination['total'] ?? 0,
                    'last_page' => $pagination['total'] ? ceil($pagination['total'] / $limit) : 1,
                    'next' => $pagination['next'] ?? null,
                    'prev' => $pagination['prev'] ?? null,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch external jobs: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'EXTERNAL_API_ERROR',
                    'message' => 'Failed to fetch external jobs',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}