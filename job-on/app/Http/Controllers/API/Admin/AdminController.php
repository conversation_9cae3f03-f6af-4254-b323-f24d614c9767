<?php

namespace App\Http\Controllers\API\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\JobBooking;
use App\Models\Booking;
use App\Models\JobReport;
use App\Enums\RoleEnum;
use App\Enums\JobBookingStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function __construct()
    {
        // Ensure all methods require authentication
        $this->middleware('auth:api');

        // Ensure only admins can access these endpoints
        $this->middleware(function ($request, $next) {
            $user = Auth::guard('api')->user();

            if (!$user || !($user->hasRole(RoleEnum::ADMIN) || $user->hasRole(RoleEnum::SUPREME_ADMIN))) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'Access denied. Only administrators can access this resource.',
                    ]
                ], 403);
            }

            return $next($request);
        });
    }

    /**
     * Get system analytics.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAnalytics(Request $request)
    {
        try {
            // Get total users count
            $totalUsers = User::count();

            // Get total jobs count
            $totalJobs = JobBooking::count();

            // Get active jobs count (open, in progress)
            $activeJobs = JobBooking::whereIn('status', [
                JobBookingStatusEnum::OPEN->value,
                JobBookingStatusEnum::IN_PROGRESS->value,
            ])->count();

            // Get completed jobs count
            $completedJobs = JobBooking::where('status', JobBookingStatusEnum::COMPLETED->value)->count();

            // Get additional analytics if requested
            $data = [
                'totalUsers' => $totalUsers,
                'totalJobs' => $totalJobs,
                'activeJobs' => $activeJobs,
                'completedJobs' => $completedJobs,
            ];

            // Add additional analytics if requested
            if ($request->has('include_providers') && $request->input('include_providers')) {
                $data['totalProviders'] = User::role(RoleEnum::PROVIDER)->count();
            }

            if ($request->has('include_customers') && $request->input('include_customers')) {
                $data['totalCustomers'] = User::role(RoleEnum::CONSUMER)->count();
            }

            return response()->json($data);

        } catch (\Exception $e) {
            Log::error('Failed to get analytics: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get analytics',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get job issue reports.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReports(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 15);
            $status = $request->input('status');

            $query = JobReport::with(['job.jobBooking', 'user', 'reviewer'])
                ->orderBy('created_at', 'desc');

            // Filter by status if provided
            if ($status) {
                $query->where('status', $status);
            }

            $reports = $query->paginate($perPage);

            $data = $reports->map(function ($report) {
                return [
                    'id' => (string) $report->id,
                    'jobId' => $report->job_uuid,
                    'reason' => $report->reason,
                    'description' => $report->description,
                ];
            });

            return response()->json([
                'reports' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get reports: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to get reports',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
