<?php

namespace App\Http\Controllers\API\Provider;

use App\Http\Controllers\Controller;
use App\Models\ProviderAvailability;
use App\Models\ProviderPortfolio;
use App\Models\ProviderService;
use App\Models\ProviderVerification;
use App\Models\User;
use App\Models\Asset;
use App\Services\AssetService;
use App\Enums\RoleEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProviderController extends Controller
{
    protected $assetService;

    public function __construct(AssetService $assetService)
    {
        $this->assetService = $assetService;

        // Ensure all methods require authentication
        $this->middleware('auth:api');

        // Ensure only providers can access these endpoints
        $this->middleware(function ($request, $next) {
            $user = Auth::guard('api')->user();

            if (!$user || !$user->hasRole(RoleEnum::PROVIDER)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'Access denied. Only providers can access this resource.',
                    ]
                ], 403);
            }

            return $next($request);
        });
    }

    /**
     * Update provider availability schedule.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAvailability(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate the request
            $validated = $request->validate([
                'monday' => 'nullable|array',
                'monday.*.start' => 'required_with:monday|date_format:H:i',
                'monday.*.end' => 'required_with:monday|date_format:H:i|after:monday.*.start',
                'tuesday' => 'nullable|array',
                'tuesday.*.start' => 'required_with:tuesday|date_format:H:i',
                'tuesday.*.end' => 'required_with:tuesday|date_format:H:i|after:tuesday.*.start',
                'wednesday' => 'nullable|array',
                'wednesday.*.start' => 'required_with:wednesday|date_format:H:i',
                'wednesday.*.end' => 'required_with:wednesday|date_format:H:i|after:wednesday.*.start',
                'thursday' => 'nullable|array',
                'thursday.*.start' => 'required_with:thursday|date_format:H:i',
                'thursday.*.end' => 'required_with:thursday|date_format:H:i|after:thursday.*.start',
                'friday' => 'nullable|array',
                'friday.*.start' => 'required_with:friday|date_format:H:i',
                'friday.*.end' => 'required_with:friday|date_format:H:i|after:friday.*.start',
                'saturday' => 'nullable|array',
                'saturday.*.start' => 'required_with:saturday|date_format:H:i',
                'saturday.*.end' => 'required_with:saturday|date_format:H:i|after:saturday.*.start',
                'sunday' => 'nullable|array',
                'sunday.*.start' => 'required_with:sunday|date_format:H:i',
                'sunday.*.end' => 'required_with:sunday|date_format:H:i|after:sunday.*.start',
            ]);

            DB::beginTransaction();

            // Delete existing availability for this provider
            ProviderAvailability::where('provider_id', $user->id)->delete();

            // Create new availability records
            $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

            foreach ($daysOfWeek as $day) {
                if (isset($validated[$day]) && is_array($validated[$day])) {
                    foreach ($validated[$day] as $timeSlot) {
                        ProviderAvailability::create([
                            'provider_id' => $user->id,
                            'day_of_week' => $day,
                            'start_time' => $timeSlot['start'],
                            'end_time' => $timeSlot['end'],
                            'is_available' => true,
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Availability updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update provider availability: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update availability',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Update provider business profile.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBusinessProfile(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate the request
            $validated = $request->validate([
                'businessName' => 'required|string|max:255',
                'contactNumber' => 'required|string|max:20',
                'website' => 'nullable|url|max:255',
            ]);

            // Update user profile
            $user->update([
                'company_name' => $validated['businessName'],
                'company_phone' => $validated['contactNumber'],
                'description' => $request->input('description', $user->description),
            ]);

            // If user has a business record, update it as well
            if ($user->business) {
                $user->business->update([
                    'name' => $validated['businessName'],
                    'phone' => $validated['contactNumber'],
                    'website' => $validated['website'] ?? $user->business->website,
                ]);
            }

            return response()->json([
                'message' => 'Business profile updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to update business profile: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to update business profile',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Add a portfolio item.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addPortfolioItem(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate the request
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:1000',
                'imageUrl' => 'nullable|string|max:500',
            ]);

            // If imageUrl is provided, validate it's a valid asset UUID
            $imageUuid = null;
            if (!empty($validated['imageUrl'])) {
                // Extract UUID from URL if it's a full URL, or use as UUID if it's just the UUID
                $imageUrl = $validated['imageUrl'];
                if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    // Extract UUID from URL path
                    $pathParts = explode('/', parse_url($imageUrl, PHP_URL_PATH));
                    $imageUuid = end($pathParts);
                } else {
                    $imageUuid = $imageUrl;
                }

                // Verify the asset exists
                $asset = Asset::where('uuid', $imageUuid)->first();
                if (!$asset) {
                    return response()->json([
                        'success' => false,
                        'error' => [
                            'code' => 'INVALID_IMAGE',
                            'message' => 'Invalid image reference provided',
                        ]
                    ], 400);
                }
            }

            // Create portfolio item
            $portfolioItem = ProviderPortfolio::create([
                'provider_id' => $user->id,
                'title' => $validated['title'],
                'description' => $validated['description'],
                'image_uuid' => $imageUuid,
                'is_active' => true,
            ]);

            return response()->json([
                'id' => $portfolioItem->uuid,
                'title' => $portfolioItem->title,
                'description' => $portfolioItem->description,
                'imageUrl' => $portfolioItem->image ? $portfolioItem->image->url : $validated['imageUrl']
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to add portfolio item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to add portfolio item',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Add a provider service.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addService(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate the request
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'price' => 'required|numeric|min:0|max:999999.99',
                'description' => 'required|string|max:1000',
            ]);

            // Create service
            $service = ProviderService::create([
                'provider_id' => $user->id,
                'name' => $validated['name'],
                'price' => $validated['price'],
                'description' => $validated['description'],
                'is_active' => true,
            ]);

            return response()->json([
                'id' => $service->uuid,
                'name' => $service->name,
                'price' => $service->price,
                'description' => $service->description
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to add service: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to add service',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Submit verification documents.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitVerificationDocuments(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();

            // Validate the request
            $validated = $request->validate([
                'document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
                'document_type' => 'required|string|in:license,insurance,certification,id_card,other',
            ]);

            // Upload the document using the asset service
            $asset = $this->assetService->upload(
                $request->file('document'),
                'verification_documents',
                [
                    'uploaded_by' => $user->id,
                    'document_type' => $validated['document_type'],
                    'original_name' => $request->file('document')->getClientOriginalName()
                ]
            );

            // Create verification record
            $verification = ProviderVerification::create([
                'provider_id' => $user->id,
                'document_uuid' => $asset->uuid,
                'document_type' => $validated['document_type'],
                'status' => 'pending',
            ]);

            return response()->json([
                'message' => 'Verification documents submitted successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'VALIDATION_ERROR',
                    'message' => 'Validation failed',
                    'details' => $e->errors(),
                ]
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to submit verification documents: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SERVER_ERROR',
                    'message' => 'Failed to submit verification documents',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
