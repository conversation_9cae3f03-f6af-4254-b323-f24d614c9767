<?php

namespace App\Enums;

/**
 * Enum for job notification campaign statuses
 */
class JobNotificationStatusEnum
{
    const PENDING = 'pending';
    const APPROVED = 'approved';
    const REJECTED = 'rejected';
    const SENT = 'sent';
    const FAILED = 'failed';
    const COMPLETED = 'completed';
    const PENDING_SCRAPING = 'pending_scraping';

    /**
     * Get all possible enum values
     *
     * @return array
     */
    public static function getValues(): array
    {
        return [
            self::PENDING,
            self::APPROVED,
            self::REJECTED,
            self::SENT,
            self::FAILED,
            self::COMPLETED,
            self::PENDING_SCRAPING,
        ];
    }
} 