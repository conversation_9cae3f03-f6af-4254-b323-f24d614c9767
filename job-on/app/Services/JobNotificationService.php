<?php

namespace App\Services;

use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Services\BusinessDiscoveryService;
use App\Mail\JobNotificationApprovalMail;
use App\Enums\JobNotificationStatusEnum;
use App\Enums\JobNotificationRecipientStatusEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class JobNotificationService
{
    protected $businessDiscoveryService;

    public function __construct(BusinessDiscoveryService $businessDiscoveryService)
    {
        $this->businessDiscoveryService = $businessDiscoveryService;
    }

    /**
     * Process business discovery and setup notification campaign
     *
     * @param JobNotificationCampaign $campaign
     * @param string $zipCode
     * @param string|null $categoryId
     * @param float|null $radius
     * @return bool Returns true if businesses were found and campaign was set up
     */
    public function processBusinessDiscovery(
        JobNotificationCampaign $campaign, 
        string $zipCode, 
        ?string $categoryId = null, 
        ?float $radius = null
    ): bool {
        $radius = $radius ?? (float) config('job_notification.default_radius');

        // Find businesses within radius
        $businesses = $this->businessDiscoveryService->findBusinesses(
            $zipCode,
            $categoryId,
            $radius
        );

        // Update campaign with business count
        $campaign->business_count = $businesses->count();
        $campaign->save();

        Log::info('Found businesses for job notification', [
            'job_id' => $campaign->job_id,
            'campaign_id' => $campaign->id,
            'business_count' => $campaign->business_count,
        ]);

        if ($campaign->business_count > 0) {
            // Create recipient records for each business
            $this->createRecipients($campaign, $businesses);
            
            // Send approval email to admin
            $this->sendAdminApprovalEmail($campaign);

            // Update campaign status to pending for admin approval
            $campaign->status = JobNotificationStatusEnum::PENDING;
            $campaign->save();

            Log::info('Job notification processed successfully and admin approval requested', [
                'job_id' => $campaign->job_id,
                'campaign_id' => $campaign->id,
                'business_count' => $campaign->business_count,
            ]);

            return true;
        }

        return false;
    }

    /**
     * Create recipient records for each business
     *
     * @param JobNotificationCampaign $campaign
     * @param \Illuminate\Support\Collection $businesses
     */
    public function createRecipients(JobNotificationCampaign $campaign, $businesses): void
    {
        foreach ($businesses as $business) {
            JobNotificationRecipient::create([
                'job_notification_campaign_id' => $campaign->id,
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_email' => $business->email,
                'business_phone' => $business->phone,
                'business_address' => $business->address,
                'status' => JobNotificationRecipientStatusEnum::PENDING,
            ]);
        }

        Log::info('Created recipient records', [
            'campaign_id' => $campaign->id,
            'recipient_count' => $businesses->count(),
        ]);
    }

    /**
     * Send admin approval email
     *
     * @param JobNotificationCampaign $campaign
     */
    public function sendAdminApprovalEmail(JobNotificationCampaign $campaign): void
    {
        try {
            $adminEmail = config('job_notification.admin_email');
            
            // Generate admin token if not exists
            if (!$campaign->admin_token || $campaign->isTokenExpired()) {
                $campaign->generateAdminToken();
            }
            
            Mail::to($adminEmail)->send(new JobNotificationApprovalMail($campaign, $campaign->admin_token));
            
            Log::info('Admin approval email sent', [
                'campaign_id' => $campaign->id,
                'admin_email' => $adminEmail,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin approval email', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
