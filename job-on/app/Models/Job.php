<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Job extends Model
{
    use HasFactory;

    protected $table = 'business_jobs';

    protected $fillable = [
        'job_uuid',
        'job_booking_id',
        'bid_id',
        'booking_id',
        'customer_id',
        'provider_id',
        'status',
        'agreed_amount',
        'estimated_completion_time',
        'actual_start_time',
        'actual_completion_time',
        'notes'
    ];

    protected $casts = [
        'job_booking_id' => 'integer',
        'bid_id' => 'integer',
        'booking_id' => 'integer',
        'customer_id' => 'integer',
        'provider_id' => 'integer',
        'agreed_amount' => 'decimal:2',
        'estimated_completion_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_completion_time' => 'datetime',
    ];

    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($job) {
            if (!$job->job_uuid) {
                $job->job_uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the job booking that owns this job
     */
    public function jobBooking(): BelongsTo
    {
        return $this->belongsTo(JobBooking::class, 'job_booking_id');
    }

    /**
     * Get the bid that created this job
     */
    public function bid(): BelongsTo
    {
        return $this->belongsTo(Bid::class, 'bid_id');
    }

    /**
     * Get the booking associated with this job
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class, 'booking_id');
    }

    /**
     * Get the customer who created the job booking
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the provider assigned to this job
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the reports for this job
     */
    public function reports(): HasMany
    {
        return $this->hasMany(JobReport::class, 'job_id');
    }

    // ==================== STATUS HELPER METHODS ====================

    /**
     * Check if job is assigned
     */
    public function isAssigned(): bool
    {
        return $this->status === 'assigned';
    }

    /**
     * Check if job is in progress
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if job is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if job is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    // ==================== WORKFLOW METHODS ====================

    /**
     * Start the job
     */
    public function start(): bool
    {
        if (!$this->isAssigned()) {
            return false;
        }

        $this->status = 'in_progress';
        $this->actual_start_time = now();
        return $this->save();
    }

    /**
     * Complete the job
     */
    public function complete(): bool
    {
        if (!$this->isInProgress()) {
            return false;
        }

        $this->status = 'completed';
        $this->actual_completion_time = now();
        return $this->save();
    }

    /**
     * Cancel the job
     */
    public function cancel(): bool
    {
        if ($this->isCompleted()) {
            return false;
        }

        $this->status = 'cancelled';
        return $this->save();
    }

    // ==================== QUERY SCOPES ====================

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get assigned jobs
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', 'assigned');
    }

    /**
     * Scope to get in progress jobs
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope to get completed jobs
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get cancelled jobs
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope to get jobs by customer
     */
    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to get jobs by provider
     */
    public function scopeByProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }
}
