<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProviderVerification extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'provider_id',
        'document_uuid',
        'document_type',
        'status',
        'admin_notes',
        'reviewed_by',
        'reviewed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reviewed_at' => 'datetime',
    ];

    /**
     * Get the provider that owns the verification.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the document asset.
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'document_uuid', 'uuid');
    }

    /**
     * Get the admin who reviewed the verification.
     */
    public function reviewer(): <PERSON>ongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }
}
