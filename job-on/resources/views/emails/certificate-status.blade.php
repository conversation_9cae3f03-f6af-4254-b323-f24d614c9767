@component('mail::message')
@if($action === 'request_review')
# Provider Certificate Review Request

Provider: **{{ $user->name }}** ({{ $user->email }}) has requested a certificate review.

@component('mail::button', ['url' => rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app'), '/') . '/admin/certificates'])
View Certificates List
@endcomponent

Please review the certificates in the admin panel.

@elseif($action === 'approved')
# Certificates Approved

Hello **{{ $user->name }}**!

Your certificates have been approved by admin.

Thank you for your cooperation!

@elseif($action === 'rejected')
# Certificates Rejected

Hello **{{ $user->name }}**!

Your certificates have been rejected by admin. Please update your certificates and request review again.

@endif

Thanks,<br>
{{ config('app.name') }}
@endcomponent 